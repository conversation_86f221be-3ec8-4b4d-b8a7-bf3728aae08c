
import torch
import rasterio
import numpy as np
from torch.utils.data import Dataset
from rasterio.enums import Resampling
from typing import List, Optional

class ECZSSRDataset(Dataset):
    """
    Dataset for EC-ZSSR.
    Loads a primary soil map and a list of environmental covariate maps,
    aligns them, and provides patches for training.

    Args:
        primary_map_path (str): Path to the primary soil map (e.g., SOC, pH).
        covariate_paths (List[str]): List of paths to environmental covariate maps.
        patch_size (int): The size of the patches to be randomly cropped from the maps.
    """
    def __init__(self, primary_map_path: str, covariate_paths: List[str], patch_size: int):
        super().__init__()

        self.primary_map_path = primary_map_path
        self.covariate_paths = covariate_paths
        self.patch_size = patch_size

        # Open the primary map to get metadata
        with rasterio.open(self.primary_map_path) as src:
            self.primary_meta = src.meta.copy()
            self.width = src.width
            self.height = src.height
            self.crs = src.crs
            self.transform = src.transform

        # Basic validation: Check if all covariates have the same CRS
        for cov_path in self.covariate_paths:
            with rasterio.open(cov_path) as cov_src:
                if cov_src.crs != self.crs:
                    raise ValueError(
                        f"CRS mismatch: Primary map has {self.crs} but {cov_path} has {cov_src.crs}. "
                        "Please reproject all covariates to the same CRS."
                    )

        # Pre-load and align all data in memory.
        # For ZSSR, we typically work with a single image, so this is feasible.
        self.aligned_data = self._load_and_align_all()

    def _load_and_align_all(self) -> torch.Tensor:
        """
        Loads the primary map and all covariates, resampling them to match the
        primary map's grid and stacking them into a single tensor.
        """
        # Load primary map
        with rasterio.open(self.primary_map_path) as src:
            primary_data = src.read(1).astype(np.float32)

        # Initialize a list to hold all channel data
        all_channels = [primary_data]

        # Load and resample covariates
        for cov_path in self.covariate_paths:
            with rasterio.open(cov_path) as cov_src:
                # Create an empty array with the same shape as the primary map
                resampled_cov = np.empty((self.height, self.width), dtype=np.float32)

                cov_src.read(
                    out=resampled_cov,
                    out_shape=(self.height, self.width),
                    resampling=Resampling.bilinear  # Or other method like cubic
                )
                all_channels.append(resampled_cov)

        # Stack all channels into a single numpy array (C, H, W)
        stacked_data = np.stack(all_channels, axis=0)

        # Convert to PyTorch tensor
        return torch.from_numpy(stacked_data)

    def __len__(self):
        """
        For ZSSR, we are training on a single image.
        To allow for multiple training iterations, we can return a large number.
        The actual number of iterations will be controlled by the training loop.
        """
        return 1000 # Arbitrary large number for iterable dataset

    def __getitem__(self, index):
        """
        Returns a randomly cropped patch from the aligned data.
        """
        # Get the full tensor C, H, W
        c, h, w = self.aligned_data.shape

        # Randomly select a top-left corner for the crop
        top = torch.randint(0, h - self.patch_size + 1, (1,)).item()
        left = torch.randint(0, w - self.patch_size + 1, (1,)).item()

        # Crop the patch
        patch = self.aligned_data[
            :,
            top : top + self.patch_size,
            left : left + self.patch_size
        ]

        # Here you could add data augmentation (e.g., random flips)
        # Example:
        # if torch.rand(1) < 0.5:
        #     patch = torch.flip(patch, dims=[1]) # Horizontal flip
        # if torch.rand(1) < 0.5:
        #     patch = torch.flip(patch, dims=[2]) # Vertical flip

        return patch

if __name__ == '__main__':
    # This is an example of how to use the dataset.
    # You would need to create some dummy data to run this.
    
    # 1. Create dummy primary map and covariate GeoTIFFs
    def create_dummy_geotiff(path, width, height, crs='EPSG:4326'):
        transform = rasterio.transform.from_origin(0, 90, 1, 1)
        with rasterio.open(
            path, 'w', driver='GTiff', height=height, width=width,
            count=1, dtype=rasterio.float32, crs=crs, transform=transform
        ) as dst:
            dst.write((np.random.rand(height, width) * 100).astype(rasterio.float32), 1)

    print("Creating dummy data for testing...")
    dummy_data_dir = 'dummy_data'
    import os
    if not os.path.exists(dummy_data_dir):
        os.makedirs(dummy_data_dir)

    primary_path = os.path.join(dummy_data_dir, 'primary_soc.tif')
    cov1_path = os.path.join(dummy_data_dir, 'cov_dem.tif')
    cov2_path = os.path.join(dummy_data_dir, 'cov_slope.tif')

    create_dummy_geotiff(primary_path, width=256, height=256)
    # Create covariate with different resolution to test resampling
    create_dummy_geotiff(cov1_path, width=128, height=128) 
    create_dummy_geotiff(cov2_path, width=256, height=256)
    
    print("Dummy data created.")

    # 2. Instantiate the dataset
    print("Instantiating dataset...")
    try:
        dataset = ECZSSRDataset(
            primary_map_path=primary_path,
            covariate_paths=[cov1_path, cov2_path],
            patch_size=64
        )
        
        # 3. Get a sample
        print("Fetching a sample patch...")
        sample_patch = dataset[0]
        
        # 4. Print information about the sample
        print(f"Successfully got a sample patch!")
        print(f"Sample patch shape: {sample_patch.shape}")
        print(f"Expected shape: (num_channels, patch_size, patch_size)")
        print(f"Number of channels (1 primary + 2 covariates): {sample_patch.shape[0]}")
        print(f"Patch size: {sample_patch.shape[1]}")

        # Test with dataloader
        from torch.utils.data import DataLoader
        dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
        batch = next(iter(dataloader))
        print(f"Successfully loaded a batch of shape: {batch.shape}")


    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        # Clean up dummy data
        import shutil
        if os.path.exists(dummy_data_dir):
            shutil.rmtree(dummy_data_dir)
            print("Cleaned up dummy data.")

