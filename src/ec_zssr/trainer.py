import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from copy import deepcopy
from tqdm import tqdm

# Import our custom modules
from .model import UNet
from .data import ECZSSRDataset
from .degradation import BicubicDownsampler

class Trainer:
    """
    The main trainer for EC-ZSSR.
    Manages the training loop, EMA updates, and inference.

    Args:
        model (nn.Module): The neural network model to be trained.
        dataset (ECZSSRDataset): The dataset for providing training patches.
        degradation (nn.Module): The module to generate LR images from HR images.
        lr (float): Learning rate.
        ema_decay (float): Decay rate for Exponential Moving Average of model weights.
    """
    def __init__(self, model: nn.Module, dataset: ECZSSRDataset, degradation: nn.Module, 
                 lr: float = 0.001, ema_decay: float = 0.999):
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")

        self.model = model.to(self.device)
        self.dataset = dataset
        self.degradation = degradation.to(self.device)

        self.lr = lr
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr)
        self.criterion = nn.L1Loss() # L1 loss is common for image restoration

        # --- EMA Setup ---
        self.ema_decay = ema_decay
        self.ema_model = self._create_ema_model()

    def _create_ema_model(self) -> nn.Module:
        """Creates a deepcopy of the model for EMA."""
        ema_model = deepcopy(self.model)
        for param in ema_model.parameters():
            param.detach_() # No gradients needed for EMA model
        return ema_model

    def _update_ema(self):
        """Update the EMA model weights."""
        with torch.no_grad():
            for ema_param, model_param in zip(self.ema_model.parameters(), self.model.parameters()):
                ema_param.data.mul_(self.ema_decay).add_(model_param.data, alpha=1 - self.ema_decay)

    def train(self, num_epochs: int, batch_size: int):
        """
        Executes the main training loop.

        Args:
            num_epochs (int): The number of epochs to train for.
            batch_size (int): The batch size for the dataloader.
        """
        dataloader = DataLoader(self.dataset, batch_size=batch_size, shuffle=True)
        num_iterations = len(dataloader) * num_epochs
        
        progress_bar = tqdm(range(num_iterations), desc="Training")

        self.model.train()
        self.ema_model.train() # Set to train mode, although it has no grads

        epoch = 0
        while epoch < num_epochs:
            for hr_patch in dataloader:
                hr_patch = hr_patch.to(self.device)

                # 1. Create LR patch using the degradation module
                # The degradation module itself handles the downsampling factor
                lr_patch = self.degradation(hr_patch)

                # 2. Forward pass
                sr_patch = self.model(lr_patch)

                # 3. Calculate loss
                loss = self.criterion(sr_patch, hr_patch)

                # 4. Backward pass and optimization
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()

                # 5. Update EMA model
                self._update_ema()

                progress_bar.update(1)
                progress_bar.set_postfix(loss=f"{loss.item():.6f}")
            
            epoch += 1
        
        progress_bar.close()
        print("Training finished.")

    @torch.no_grad()
    def inference(self, lr_image: torch.Tensor) -> torch.Tensor:
        """
        Performs super-resolution on a low-resolution image using the EMA model.

        Args:
            lr_image (torch.Tensor): The low-resolution input image (B, C, H, W).

        Returns:
            torch.Tensor: The super-resolved output image.
        """
        print("Performing inference with the EMA model...")
        self.ema_model.eval()
        lr_image = lr_image.to(self.device)
        sr_image = self.ema_model(lr_image)
        return sr_image.cpu()


if __name__ == '__main__':
    # This is a conceptual example of how to use the Trainer.
    # It requires dummy data to be set up first.

    print("Setting up a dummy training scenario...")

    # 1. Create dummy data and dataset
    # Note: This part is illustrative. We'll use our actual data.py structure.
    # In a real script, you would get these paths from command line arguments.
    
    # Create dummy files for the dataset to use
    import os
    import rasterio as rio
    dummy_data_dir = 'dummy_data_trainer'
    if not os.path.exists(dummy_data_dir):
        os.makedirs(dummy_data_dir)

    primary_path = os.path.join(dummy_data_dir, 'primary.tif')
    cov_path = os.path.join(dummy_data_dir, 'cov.tif')
    
    def create_dummy_geotiff(path, w, h):
        with rio.open(path, 'w', driver='GTiff', width=w, height=h, count=1, dtype=rio.float32) as dst:
            dst.write(np.random.rand(h, w).astype(np.float32), 1)

    create_dummy_geotiff(primary_path, 256, 256)
    create_dummy_geotiff(cov_path, 256, 256)

    # 2. Setup components
    try:
        dataset = ECZSSRDataset(
            primary_map_path=primary_path,
            covariate_paths=[cov_path],
            patch_size=64
        )

        # The input to the model has 1 (primary) + 1 (covariate) = 2 channels
        # The output should be the SR version of the primary map, so 1 channel
        model = UNet(in_channels=2, out_channels=1)

        # We want to upscale by 4x, so the degradation should downscale by 4x
        degradation = BicubicDownsampler(scale_factor=4)

        # 3. Instantiate and run the trainer
        trainer = Trainer(model=model, dataset=dataset, degradation=degradation, lr=1e-4)
        
        print("Starting dummy training...")
        # In a real run, num_epochs would be much larger
        trainer.train(num_epochs=1, batch_size=2)

        # 4. Test inference
        # Create a dummy LR image that matches the degradation output size
        # Input to model should be (B, C, H/scale, W/scale)
        dummy_lr_image = torch.randn(1, 2, 16, 16) # 64/4 = 16
        sr_output = trainer.inference(dummy_lr_image)

        print(f"Inference input shape: {dummy_lr_image.shape}")
        print(f"Inference output shape: {sr_output.shape}")
        # The output of U-Net will have the same spatial dimensions as the input
        assert list(sr_output.shape) == [1, 1, 16, 16]
        print("Trainer test finished successfully.")

    except Exception as e:
        print(f"An error occurred during the test: {e}")
    finally:
        # Clean up
        import shutil
        if os.path.exists(dummy_data_dir):
            shutil.rmtree(dummy_data_dir)
            print("Cleaned up dummy data.")