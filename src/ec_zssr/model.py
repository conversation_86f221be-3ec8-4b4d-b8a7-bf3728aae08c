import torch
import torch.nn as nn
import torch.nn.functional as F

class DoubleConv(nn.Module):
    """
    (convolution => [BN] => ReLU) * 2
    
    This basic building block is widely used in U-Net architectures for 
    remote sensing image processing, providing effective feature extraction
    while maintaining spatial information crucial for environmental mapping.
    """

    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """Downscaling with maxpool then double conv"""

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """Upscaling then double conv"""

    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()

        # if bilinear, use the normal convolutions to reduce the number of channels
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        # input is CHW
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]

        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)


class OutConv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)

    def forward(self, x):
        return self.conv(x)


class UNet(nn.Module):
    """
    A flexible U-Net implementation for EC-ZSSR (Environmentally-Covariant Zero-Shot Super-Resolution).
    
    This architecture is specifically designed for remote sensing applications where multi-source
    environmental data (e.g., DEM, NDVI, climate variables) are fused with primary soil maps
    to achieve enhanced spatial resolution. The U-Net's encoder-decoder structure with skip
    connections preserves both local details and global context, crucial for environmental
    variable mapping from satellite/airborne remote sensing data.

    Args:
        in_channels (int): Number of input channels (1 for primary soil map + N for environmental covariates).
        out_channels (int): Number of output channels (typically 1 for the super-resolved soil map).
        bilinear (bool): Whether to use bilinear upsampling or transposed convolutions for upsampling.
        
    References:
        - Ronneberger et al. (2015). U-Net: Convolutional Networks for Biomedical Image Segmentation.
        - Application to environmental remote sensing: Enhanced spatial resolution soil mapping.
    """
    def __init__(self, in_channels: int, out_channels: int, bilinear: bool = True):
        super(UNet, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.bilinear = bilinear

        self.inc = DoubleConv(in_channels, 64)
        self.down1 = Down(64, 128)
        self.down2 = Down(128, 256)
        self.down3 = Down(256, 512)
        factor = 2 if bilinear else 1
        self.down4 = Down(512, 1024 // factor)
        self.up1 = Up(1024, 512 // factor, bilinear)
        self.up2 = Up(512, 256 // factor, bilinear)
        self.up3 = Up(256, 128 // factor, bilinear)
        self.up4 = Up(128, 64, bilinear)
        self.outc = OutConv(64, out_channels)

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        logits = self.outc(x)
        return logits

if __name__ == '__main__':
    # This is an example of how to use the model.
    
    # 1. Define model parameters
    # Let's assume we have 1 primary map (e.g., SOC) and 4 environmental covariates
    input_channels = 1 + 4 
    output_channels = 1 # We want to predict the SR version of the primary map
    
    # 2. Instantiate the model
    print(f"Instantiating U-Net with {input_channels} input channels and {output_channels} output channel...")
    model = UNet(in_channels=input_channels, out_channels=output_channels)

    # 3. Create a dummy input tensor
    # This simulates a batch of data from our ECZSSRDataset
    batch_size = 4
    patch_size = 64 # Should match the patch_size in the dataset
    dummy_input = torch.randn(batch_size, input_channels, patch_size, patch_size)
    print(f"Created a dummy input tensor of shape: {dummy_input.shape}")

    # 4. Perform a forward pass
    print("Performing a forward pass...")
    with torch.no_grad():
        output = model(dummy_input)
    
    # 5. Print information about the output
    print("Forward pass successful!")
    print(f"Output tensor shape: {output.shape}")
    print(f"Expected output shape: ({batch_size}, {output_channels}, {patch_size}, {patch_size})")

    # Verify that the output shape matches the input spatial dimensions
    assert output.shape == (batch_size, output_channels, patch_size, patch_size), \
        "Output shape is incorrect!"
    
    print("\nModel test passed!")


