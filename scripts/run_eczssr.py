import argparse
import torch
import rasterio
import numpy as np
import os
import sys

# Add the src directory to the Python path to allow for absolute imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.ec_zssr.data import ECZSSRDataset
from src.ec_zssr.model import UNet
from src.ec_zssr.degradation import BicubicDownsampler, KernelGAN
from src.ec_zssr.trainer import Trainer

def main(args):
    """Main function to run the EC-ZSSR process."""
    print("--- Starting EC-ZSSR ---")
    print(f"Primary Map: {args.primary_map}")
    print(f"Covariate Maps: {args.covariate_maps}")
    print(f"Output Path: {args.output_path}")
    print(f"Scale Factor: {args.scale_factor}x")

    # 1. Setup Dataset
    print("\n1. Initializing Dataset...")
    dataset = ECZSSRDataset(
        primary_map_path=args.primary_map,
        covariate_paths=args.covariate_maps,
        patch_size=args.patch_size
    )
    print(f"Dataset created. Number of training steps per epoch: {len(dataset)}")

    # 2. Setup Model, Degradation, and Trainer
    print("\n2. Initializing Model & Trainer...")
    in_channels = 1 + len(args.covariate_maps)
    model = UNet(in_channels=in_channels, out_channels=1) # Output is the SR primary map

    if args.degradation == 'bicubic':
        degradation = BicubicDownsampler(scale_factor=args.scale_factor)
    elif args.degradation == 'kernelgan':
        # Note: KernelGAN requires its own training loop, which is a placeholder for now.
        # The trainer will use the placeholder kernel.
        degradation = KernelGAN(scale_factor=args.scale_factor)
    else:
        raise ValueError(f"Unknown degradation mode: {args.degradation}")

    trainer = Trainer(
        model=model,
        dataset=dataset,
        degradation=degradation,
        lr=args.lr,
        ema_decay=args.ema_decay
    )
    print("Model, Degradation, and Trainer are ready.")

    # 3. Train the model
    print("\n3. Starting Training...")
    trainer.train(num_epochs=args.epochs, batch_size=args.batch_size)

    # 4. Perform Inference
    print("\n4. Performing Inference on the full image...")
    # The dataset already holds the full, aligned tensor. We can use it directly.
    # We need to add a batch dimension (B=1) for the model.
    full_lr_tensor = dataset.aligned_data.unsqueeze(0)
    
    # The UNet model is trained to map from a degraded space back to the original space.
    # So for inference, we feed it the original LR image to get the SR image.
    # The trainer's inference method uses the EMA model for stable results.
    sr_output_tensor = trainer.inference(full_lr_tensor)

    # Remove batch dimension and convert to numpy array (C, H, W)
    sr_output_numpy = sr_output_tensor.squeeze(0).cpu().numpy()
    print(f"Inference complete. Output shape: {sr_output_numpy.shape}")

    # 5. Save the output as a GeoTIFF
    print("\n5. Saving Super-Resolved map...")
    
    # Get metadata from the original primary map
    src_meta = dataset.primary_meta
    orig_transform = src_meta['transform']

    # Create a new Affine transform for the output
    # The pixel size is divided by the scale factor
    new_transform = rasterio.transform.Affine(
        orig_transform.a / args.scale_factor,
        orig_transform.b,
        orig_transform.c,
        orig_transform.d,
        orig_transform.e / args.scale_factor,
        orig_transform.f
    )

    # Update metadata for the output file
    output_meta = src_meta.copy()
    output_meta.update({
        'driver': 'GTiff',
        'height': src_meta['height'] * args.scale_factor,
        'width': src_meta['width'] * args.scale_factor,
        'transform': new_transform,
        'count': 1, # Output is single channel
        'dtype': 'float32'
    })

    # Write to file
    with rasterio.open(args.output_path, 'w', **output_meta) as dst:
        dst.write(sr_output_numpy.astype(np.float32))

    print(f"\nSuccessfully saved output to: {args.output_path}")
    print("--- EC-ZSSR Finished ---")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="End-to-End EC-ZSSR.")

    # --- Required Arguments ---
    parser.add_argument('--primary_map', type=str, required=True, help='Path to the primary low-resolution map (e.g., SOC.tif).')
    parser.add_argument('--covariate_maps', type=str, nargs='+', required=True, help='List of paths to covariate maps (e.g., DEM.tif Slope.tif).')
    parser.add_argument('--output_path', type=str, required=True, help='Path to save the super-resolved output GeoTIFF.')

    # --- Model & Training Hyperparameters ---
    parser.add_argument('--scale_factor', type=int, default=4, help='The super-resolution scale factor.')
    parser.add_argument('--patch_size', type=int, default=64, help='Size of patches for training.')
    parser.add_argument('--epochs', type=int, default=10, help='Number of training epochs.')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate.')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size for training.')
    parser.add_argument('--ema_decay', type=float, default=0.999, help='Decay rate for EMA model.')
    parser.add_argument('--degradation', type=str, default='bicubic', choices=['bicubic', 'kernelgan'], help='Degradation method to use.')

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output_path)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    main(args)